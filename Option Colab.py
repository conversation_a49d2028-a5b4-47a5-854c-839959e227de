# TradingAgents: Multi-Agent LLM Options Trading Framework
#
# Version: 2.0
# Description: This script implements an enhanced multi-agent LLM system for simulating SPY ETF options trading decisions.
# It features specialized analyst roles, including a quantitative-style option pricing analyst, and incorporates
# portfolio state awareness into the final trading decision.
#
# Author: AI Systems Architect & Quantitative Developer
# Date: 2024-05-17
#

import os
import re
import json
import math
import logging
import time
import requests
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
import yfinance as yf
import pandas_datareader.data as web
# OpenRouter API will be accessed via requests

# Pandas configuration for Sperandeo functions
pd.options.mode.chained_assignment = None  # silence chained assignment warnings
pd.set_option('future.no_silent_downcasting', True)  # suppress downcasting warnings

# --- CONFIGURATION ---

# 1. API Keys 
OPENROUTER_API_KEY = "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"
FRED_API_KEY = "dac90176cd11cbf8281068404bc30b7a"

# 2. LLM Configuration
LLM_MODEL = "x-ai/grok-4-fast:free"

# 3. Simulation Parameters
ANALYSIS_WINDOW_DAYS = 126 # Approx. 6 months of trading days
HV_WINDOW = 21 # Historical Volatility window (approx. 1 month)
DTE_CHOICES = [5, 8, 15, 22, 29, 36, 43, 57, 64, 85, 97, 113, 127] # Days to Expiration

# 4. Data Tickers and Series
MARKET_TICKERS = ["SPY", "^VIX", "^IRX"] # SPY ETF, VIX Index, 13-week T-bill (^IRX as risk-free rate)
FRED_SERIES = {
    "T10Y2Y": "10-Year Minus 2-Year Treasury Yield Spread"
}

# 5. Logging Setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# --- SPERANDEO TECHNICAL ANALYSIS FUNCTIONS ---

def get_spy_market_data(ticker: str = "SPY", days: int = 220) -> pd.DataFrame:
    """
    Fetches the last N trading days of OHLCV market data for SPY.
    Enhanced version for Sperandeo analysis requiring full OHLCV data.
    """
    logging.info(f"Fetching last {days} trading days of OHLCV data for {ticker}...")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=int(days * 2.2))
    data = yf.download(
        ticker,
        start=start_date.strftime("%Y-%m-%d"),
        end=end_date.strftime("%Y-%m-%d"),
        progress=False,
        auto_adjust=False,
        ignore_tz=True,
        interval="1d",
    )
    if data.empty:
        raise ValueError(f"Failed to fetch OHLCV data for {ticker}")

    # Handle MultiIndex columns if present
    if isinstance(data.columns, pd.MultiIndex):
        data.columns = data.columns.droplevel(1)

    data = data.dropna()
    data = data.tail(days).copy()
    logging.info(f"OHLCV data fetched: {len(data)} rows.")
    return data


def _local_extrema_flags(df: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
    """
    Basic local maxima/minima on a one-bar neighborhood.
    """
    is_peak = (df["High"] > df["High"].shift(1)) & (df["High"] > df["High"].shift(-1))
    is_trough = (df["Low"] < df["Low"].shift(1)) & (df["Low"] < df["Low"].shift(-1))
    return is_peak.fillna(False), is_trough.fillna(False)


def identify_trend(df: pd.DataFrame) -> pd.DataFrame:
    """
    Identifies trend regime using swing structure:
    - Uptrend: higher highs and higher lows
    - Downtrend: lower highs and lower lows
    - Else: sideways (0)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    peaks = df["High"].where(is_peak)
    troughs = df["Low"].where(is_trough)

    last_peak = peaks.ffill()
    prev_peak = peaks.ffill().shift(1)
    last_trough = troughs.ffill()
    prev_trough = troughs.ffill().shift(1)

    is_uptrend = (last_peak > prev_peak) & (last_trough > prev_trough)
    is_downtrend = (last_peak < prev_peak) & (last_trough < prev_trough)

    df["trend_state"] = 0
    df.loc[is_uptrend, "trend_state"] = 1
    df.loc[is_downtrend, "trend_state"] = -1
    return df


def quantify_consecutive_days(df: pd.DataFrame) -> pd.DataFrame:
    """
    Computes signed streak length of consecutive up/down closes.
    Positive values: up-day streak length; Negative values: down-day streak length.
    """
    df = df.copy()
    price_change = df["Close"].diff()
    sign = np.sign(price_change).fillna(0)
    grp = (sign != sign.shift()).cumsum()
    counts = sign.groupby(grp).cumcount() + 1
    df["consecutive_days"] = counts * sign
    return df


def detect_trendline_break(df: pd.DataFrame) -> pd.DataFrame:
    """
    Approximates trendline breaks using the last two swing lows (uptrend) or highs (downtrend).
    - For uptrend line: connect last two swing lows; if Close crosses below the extrapolated line -> bearish break (+1).
    - For downtrend line: connect last two swing highs; if Close crosses above the extrapolated line -> bullish break (-1).

    Output:
      trendline_break:
        +1: bearish break of uptrend line
        -1: bullish break of downtrend line
         0: none
      trendline_slope_up, trendline_slope_down: slopes for context (NaN if not defined)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    n = len(df)
    tl_break = np.zeros(n, dtype=int)
    slope_up = np.full(n, np.nan)
    slope_down = np.full(n, np.nan)

    lows: List[Tuple[int, float]] = []
    highs: List[Tuple[int, float]] = []

    last_up_cross = False
    last_down_cross = False

    closes = df["Close"].values
    lows_arr = df["Low"].values
    highs_arr = df["High"].values

    for i in range(n):
        if is_trough.iloc[i]:
            lows.append((i, lows_arr[i]))
            if len(lows) > 2:
                lows.pop(0)
        if is_peak.iloc[i]:
            highs.append((i, highs_arr[i]))
            if len(highs) > 2:
                highs.pop(0)

        # Uptrend line from two recent swing lows
        if len(lows) == 2:
            (x1, y1), (x2, y2) = lows[0], lows[1]
            if x2 != x1:
                m_up = (y2 - y1) / (x2 - x1)
                slope_up[i] = m_up
                y_line = y1 + m_up * (i - x1)
                crossed = closes[i] < y_line
                if crossed and not last_up_cross and i >= x2:
                    tl_break[i] = 1  # bearish break
                last_up_cross = crossed

        # Downtrend line from two recent swing highs
        if len(highs) == 2:
            (x1h, y1h), (x2h, y2h) = highs[0], highs[1]
            if x2h != x1h:
                m_dn = (y2h - y1h) / (x2h - x1h)
                slope_down[i] = m_dn
                y_line_dn = y1h + m_dn * (i - x1h)
                crossed_dn = closes[i] > y_line_dn
                if crossed_dn and not last_down_cross and i >= x2h:
                    tl_break[i] = -1  # bullish break
                last_down_cross = crossed_dn

    df["trendline_break"] = tl_break
    df["trendline_slope_up"] = slope_up
    df["trendline_slope_down"] = slope_down
    return df


def apply_123_rule(df: pd.DataFrame, use_trendline_condition: bool = True) -> pd.DataFrame:
    """
    Applies Sperandeo's 1-2-3 Rule, tracking "armed" and "triggered" states.

    1-2-3 Top:
      1) Break of uptrend line (if use_trendline_condition=True, require trendline_break=+1)
      2) Lower high forms
      3) Price breaks below last swing low -> trigger

    1-2-3 Bottom: symmetric.

    Output:
      123_reversal_state:
        +1.0: Top Triggered
        +0.5: Top Armed (lower high formed after condition 1)
        -1.0: Bottom Triggered
        -0.5: Bottom Armed (higher low formed after condition 1)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    peak_vals = df["High"].where(is_peak)
    trough_vals = df["Low"].where(is_trough)

    last_peak = peak_vals.ffill()
    last_trough = trough_vals.ffill()

    # 2) Lower high formed? (vs previous peak)
    prev_peak_at_peaks = peak_vals.shift(1)
    lower_high_now = is_peak & (df["High"] < prev_peak_at_peaks)

    # 2) Higher low formed? (vs previous trough)
    prev_trough_at_troughs = trough_vals.shift(1)
    higher_low_now = is_trough & (df["Low"] > prev_trough_at_troughs)

    # Optional: enforce Condition 1 via trendline breaks (from detect_trendline_break)
    if use_trendline_condition and "trendline_break" in df.columns:
        cond1_top = df["trendline_break"].fillna(0).astype(int) == 1     # bearish break of uptrend line
        cond1_bot = df["trendline_break"].fillna(0).astype(int) == -1    # bullish break of downtrend line
    else:
        # If not using trendline condition, allow arms to form without it
        cond1_top = pd.Series(True, index=df.index)
        cond1_bot = pd.Series(True, index=df.index)

    # Persist "armed" state forward after Condition 1 occurs
    df["top_armed"] = (cond1_top & lower_high_now).replace(False, np.nan).ffill().fillna(False).infer_objects(copy=False)
    df["bottom_armed"] = (cond1_bot & higher_low_now).replace(False, np.nan).ffill().fillna(False).infer_objects(copy=False)

    # 3) Trigger when price breaches last swing low (top) or last swing high (bottom)
    break_below_last_trough = df["Close"] < last_trough
    break_above_last_peak = df["Close"] > last_peak

    top_triggered = df["top_armed"] & break_below_last_trough
    bottom_triggered = df["bottom_armed"] & break_above_last_peak

    state = np.zeros(len(df))
    state[df["top_armed"]] = 0.5
    state[df["bottom_armed"]] = -0.5
    state[top_triggered] = 1.0
    state[bottom_triggered] = -1.0

    df["123_reversal_state"] = state

    # Clean up helper columns
    df.drop(columns=["top_armed", "bottom_armed"], inplace=True)
    return df


def apply_2b_rule(df: pd.DataFrame, lookback: int = 5) -> pd.DataFrame:
    """
    Applies Sperandeo's 2B rule (failed breakout/breakdown) with a configurable lookback.
    Adds 2B amplitude (strength) based on overshoot relative to the reference swing level.

    Output:
      2b_signal:
        +1: 2B Top (failed breakout) -> Bearish
        -1: 2B Bottom (failed breakdown) -> Bullish
         0: none
      2b_strength: overshoot/undershoot magnitude (0..)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    last_peak = df["High"].where(is_peak).ffill()
    last_trough = df["Low"].where(is_trough).ffill()

    # Breakout above last peak in recent window, then close back below that peak level -> 2B Top
    breakout = df["High"] > last_peak.shift(1)
    breakout_recent = breakout.rolling(window=lookback, min_periods=1).max().astype(bool)
    failed_breakout = df["Close"] < last_peak.shift(1)
    top_2b = breakout_recent & failed_breakout

    # Breakdown below last trough in recent window, then close back above that trough -> 2B Bottom
    breakdown = df["Low"] < last_trough.shift(1)
    breakdown_recent = breakdown.rolling(window=lookback, min_periods=1).max().astype(bool)
    failed_breakdown = df["Close"] > last_trough.shift(1)
    bottom_2b = breakdown_recent & failed_breakdown

    # De-duplicate contiguous True spans (flag only first bar of a new event)
    top_trigger = top_2b & (~top_2b.shift(1).fillna(False).infer_objects(copy=False))
    bottom_trigger = bottom_2b & (~bottom_2b.shift(1).fillna(False).infer_objects(copy=False))

    sig = np.zeros(len(df), dtype=int)
    sig[top_trigger] = 1
    sig[bottom_trigger] = -1
    df["2b_signal"] = sig

    # 2B amplitude (strength): max overshoot/undershoot in the lookback window prior to trigger
    strength = np.zeros(len(df))
    for i in np.where(top_trigger)[0]:
        ref = float(last_peak.shift(1).iloc[i]) if not np.isnan(last_peak.shift(1).iloc[i]) else np.nan
        if not np.isnan(ref) and ref != 0:
            lo = max(0, i - lookback + 1)
            overshoot = (df["High"].iloc[lo:i+1].max() - ref) / abs(ref)
            strength[i] = max(0.0, overshoot)
    for i in np.where(bottom_trigger)[0]:
        ref = float(last_trough.shift(1).iloc[i]) if not np.isnan(last_trough.shift(1).iloc[i]) else np.nan
        if not np.isnan(ref) and ref != 0:
            lo = max(0, i - lookback + 1)
            undershoot = (ref - df["Low"].iloc[lo:i+1].min()) / abs(ref)
            strength[i] = max(0.0, undershoot)
    df["2b_strength"] = strength

    return df


def apply_four_day_rule(df: pd.DataFrame) -> pd.DataFrame:
    """
    Implements the spirit of Sperandeo's Four-Day Rule:
      - After 4+ consecutive up days, the first down day is a potential bearish reversal day.
      - After 4+ consecutive down days, the first up day is a potential bullish reversal day.

    Output:
      four_day_signal:
        +1: bearish reversal day after >=4 up days
        -1: bullish reversal day after >=4 down days
         0: none
    """
    df = df.copy()
    df = quantify_consecutive_days(df)
    price_change = df["Close"].diff()
    sign = np.sign(price_change).fillna(0)

    prev_streak = df["consecutive_days"].shift(1).fillna(0)
    current_sign = sign

    bearish_reversal = (prev_streak >= 4) & (current_sign < 0)
    bullish_reversal = (prev_streak <= -4) & (current_sign > 0)

    sig = np.zeros(len(df), dtype=int)
    sig[bearish_reversal] = 1
    sig[bullish_reversal] = -1
    df["four_day_signal"] = sig
    return df


def generate_sperandeo_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Master function to compute all Sperandeo-related features.
    """
    features = df.copy()
    features = identify_trend(features)
    features = detect_trendline_break(features)
    features = apply_123_rule(features, use_trendline_condition=True)
    features = apply_2b_rule(features, lookback=5)
    features = apply_four_day_rule(features)
    return features


def construct_sperandeo_prompt(spy_ohlcv: pd.DataFrame,
                              sperandeo_features: pd.DataFrame,
                              market_data: pd.DataFrame,
                              fred_data: pd.DataFrame) -> str:
    """
    Construct a detailed prompt for Victor Sperandeo technical analysis.
    Adapted for Option Colab multi-agent framework.
    """
    if sperandeo_features.empty:
        return "No technical analysis data available."

    latest = sperandeo_features.iloc[-1]

    # Include 2B strength in prompt
    twob_strength = latest.get("2b_strength", 0.0)

    # Prepare SPY data for analysis (last 50 days for context)
    spy_data_str = spy_ohlcv[['Open', 'High', 'Low', 'Close', 'Volume']].tail(50).round(2).to_string()

    # Market context from existing data
    market_str = market_data.tail(10).to_string()

    prompt = f"""You are Victor Sperandeo ("Trader Vic"), the legendary trader and author of "Trader Vic - Methods of a Wall Street Master." Your analysis is disciplined, methodical, and grounded in your specific rules for identifying trend reversals.

Your task is to provide a comprehensive technical analysis of SPY using your signature methodology for the Option Colab trading system.

METHODOLOGY:
At the heart of your approach is disciplined trend change confirmation using these specific technical indicators:

TREND LINE ANALYSIS: You employ objective trend line drawing:
- Uptrend: Line from lowest low to highest minor low preceding the highest high
- Downtrend: Line from highest high to lowest minor high before the lowest low
- Breaking of this trend line is the initial signal of potential trend change

THE 1-2-3 RULE (Your signature three-step reversal framework):
- Step 1: Break of the established trend line
- Step 2: Test of the high/low - price attempts to retest recent high/low but fails
- Step 3: Move beyond previous minor price point
- ALL THREE conditions must be fulfilled to confirm trend reversal

THE 2B PATTERN (Your most heavily weighted criterion):
- Price moves beyond previous high/low but fails to sustain breakout and quickly reverses
- This "false breakout" indicates momentum loss and is your strongest indicator

THE FOUR-DAY RULE (Market climax identification):
- Four consecutive days moving in trend direction, followed by reversal
- Suggests high probability of trend change within intermediate trend

CURRENT MARKET DATA:
Recent SPY Price Action (Last 50 Days):
{spy_data_str}

Current Market Context:
{market_str}

PRE-COMPUTED SPERANDEO INDICATORS (Latest Day):
- Trend State: {latest['trend_state']:.1f} (1: Uptrend, -1: Downtrend, 0: Sideways)
- 1-2-3 Reversal: {latest['123_reversal_state']:.1f} (1: Top Triggered, 0.5: Top Armed, -1: Bottom Triggered, -0.5: Bottom Armed)
- 2B Pattern: {latest['2b_signal']:.1f} (1: 2B Top/Bearish, -1: 2B Bottom/Bullish)
- 2B Strength: {twob_strength:.3f} (Magnitude of the false breakout)
- Four-Day Rule: {latest.get('four_day_signal', 0):.1f} (1: Bearish, -1: Bullish)
- Trendline Break: {latest.get('trendline_break', 0):.1f} (+1: Bearish break, -1: Bullish break)
- Consecutive Days: {latest['consecutive_days']:.1f}

ANALYSIS REQUIREMENTS:
1. **Primary Trend Assessment**: What is the current dominant trend? Has the trendline been broken?
2. **1-2-3 Rule Status**: Is a 1-2-3 reversal pattern forming or confirmed?
3. **2B Pattern Analysis**: Do you see evidence of a 2B top or bottom? This is your most important criterion.
4. **Key Support/Resistance Levels**: Identify critical price levels based on your swing analysis.
5. **Recent Momentum**: Analyze the last 5-10 trading days for momentum shifts.
6. **Overall Market Bias**: Based on your complete analysis, state your market bias: Bullish, Bearish, or Neutral.

Provide your analysis in your characteristic disciplined, methodical style. Be specific about what you see in the price action and why it matters according to your proven methodology.

Your output should be a comprehensive technical analysis report that will be used by other analysts in the Option Colab system."""

    return prompt


# --- AGENT SYSTEM PROMPTS (Version 2.0) ---

AGENT_PROMPTS = {
    # Note: technical_analyst is now handled by SperandeoTechnicalAnalyst class
    "economist_sentiment_analyst": """
You are a Macroeconomist and Sentiment Specialist. Your task is to analyze the provided economic (FRED) and volatility (VIX) data to paint a picture of the overall market mood and economic health.

Your analysis must cover:
1.  **Economic Outlook:** Based on Unemployment, CPI, and the Yield Curve, what is the current state of the economy? Are conditions inflationary, deflationary, expanding, or contracting?
2.  **Market Sentiment:** Interpret the VIX level and its recent trend. Is the market fearful or complacent? Is the 'fear gauge' rising or falling?
3.  **Synthesis:** Combine the economic and sentiment data. Is the market sentiment justified by the economic reality?

Provide your output as a single, coherent report.
""",
    "option_pricing_analyst": f"""
You are a quantitative analyst specializing in options volatility. Your task is to assess whether buying long SPY options is favorable based on the relationship between implied volatility (VIX) and historical volatility (HV). You must adhere to the fund's research findings.

**Core Logic:**
1.  **Compare VIX to HV:** The primary indicator is the spread between VIX and the {HV_WINDOW}-day HV.
2.  **Assess Favorability:**
    - If VIX is more than 10% above HV (e.g., VIX=20, HV=18), options are considered 'overpriced'. The premium drag makes buying them **unfavorable**.
    - If VIX is below HV, options are 'underpriced' and buying is **favorable**.
    - The default bias is **unfavorable** due to the consistent underperformance of long options found in academic research. You need a compelling reason (like VIX < HV) to call it favorable.
3.  **Directional Consideration:** A very high VIX might be acceptable for a bearish long put strategy if a crash is anticipated, but it is highly unfavorable for a long call strategy. Mention this nuance.
4.  **Carry Cost:** Note the impact of the risk-free rate (^IRX). A higher rate increases the carry cost for long option positions, making it an additional headwind for buyers.
5.  **Expiry Suggestion:**
    - If VIX is high or has spiked recently, suggest **'near-term'** expiries (<= 43 DTE) to capture the immediate move.
    - If VIX is low or compressed, suggest **'long-term'** expiries (> 43 DTE) for a better value proposition.

Your output MUST be a single JSON object with the following structure:
{{
    "vol_assessment": "'favorable' or 'unfavorable'",
    "calibrated_confidence": "An integer from 0 (no confidence in assessment) to 100 (absolute confidence)",
    "reasoning": "A detailed explanation covering your VIX vs. HV comparison, the impact of the risk-free rate, and the default unfavorable bias.",
    "suggested_expiry_type": "'near-term' or 'long-term'"
}}
""",
    "bullish_strategist": """
You are an aggressive and optimistic Bullish Strategist. Using the provided reports from the Technical, Economic, and Options analysts, build the strongest possible case for a positive (upward) move in the market.

-   Focus on any data point that supports a bullish outlook (e.g., price at support, positive economic data, favorable volatility).
-   Downplay or re-interpret any negative data.
-   Conclude with a clear "Bullish Case" summary and a confidence score for your argument.

Your output must be a text report ending with the line:
CONFIDENCE: [a number from 1 to 10]
""",
    "bearish_strategist": """
You are a cautious and pessimistic Bearish Strategist. Using the provided reports from the Technical, Economic, and Options analysts, build the strongest possible case for a negative (downward) move in the market.

-   Focus on any data point that supports a bearish outlook (e.g., price at resistance, negative economic data, unfavorable volatility).
-   Downplay or re-interpret any positive data.
-   Conclude with a clear "Bearish Case" summary and a confidence score for your argument.

Your output must be a text report ending with the line:
CONFIDENCE: [a number from 1 to 10]
""",
    "chief_trader": f"""
You are the Chief Trader. You have received reports from your Technical, Economic, and Option Pricing analysts, the conflicting arguments from your strategists, and information on your current portfolio.

Your task is to synthesize all this information and make a single, decisive, and actionable trading decision for SPY options for the next trading day. You must weigh all evidence, including the quantitative assessment of option pricing.

Your final output MUST be a single JSON object with the following structure:
{{
    "decision": "BULLISH", "BEARISH", or "NEUTRAL",
    "strategy": "A specific options strategy (e.g., 'Buy Call', 'Buy Put', 'Bull Call Spread', 'Bear Put Spread', 'Iron Condor', 'No Trade')",
    "dte": An integer representing the chosen Days to Expiration from this list: {DTE_CHOICES},
    "confidence": An integer from 1 (very low) to 10 (very high) in your overall decision,
    "reasoning": "A detailed paragraph explaining how you synthesized ALL inputs (technical, economic, volatility, strategist debate, and current position) to arrive at your final decision."
}}
"""
}


# --- CORE FRAMEWORK CLASSES ---

class DataFetcher:
    """Handles all external data fetching and pre-processing, including HV calculation."""
    def __init__(self):
        self.data_cache = {}

    def _calculate_hv(self, series: pd.Series) -> pd.Series:
        """Calculates annualized historical volatility."""
        log_returns = np.log(series / series.shift(1))
        hv = log_returns.rolling(window=HV_WINDOW).std() * np.sqrt(252)
        return hv * 100 # Return as percentage

    def get_market_data(self, current_date: datetime) -> dict:
        date_str = current_date.strftime('%Y-%m-%d')
        if date_str in self.data_cache:
            return self.data_cache[date_str]

        logging.info(f"Fetching data for window ending on {date_str}...")
        end_date = current_date
        start_date = end_date - timedelta(days=ANALYSIS_WINDOW_DAYS * 1.7)

        try:
            market_data_raw = yf.download(MARKET_TICKERS, start=start_date, end=end_date, progress=False)
            if market_data_raw.empty:
                logging.error(f"No market data found for the period ending {date_str}.")
                return None

            market_data = market_data_raw['Close'].tail(ANALYSIS_WINDOW_DAYS).copy()
            market_data['HV'] = self._calculate_hv(market_data['SPY'])

            # Fetch OHLCV data for SPY for Sperandeo analysis (extended window for technical analysis)
            try:
                spy_ohlcv = get_spy_market_data("SPY", days=220)
                logging.info(f"Fetched SPY OHLCV data: {len(spy_ohlcv)} rows")
            except Exception as e:
                logging.warning(f"Failed to fetch SPY OHLCV data: {e}. Using Close data only.")
                spy_ohlcv = None

            fred_data = web.DataReader(list(FRED_SERIES.keys()), 'fred', start_date, end_date)
            fred_data = fred_data.tail(ANALYSIS_WINDOW_DAYS)

            # Fill NaNs robustly (using newer pandas methods)
            for df in [market_data, fred_data]:
                df.ffill(inplace=True)
                df.bfill(inplace=True)

            if market_data.isnull().values.any() or fred_data.isnull().values.any():
                 logging.critical(f"Unrecoverable NaN values in data for {date_str}. Skipping.")
                 return None

            result = {
                "market_data": market_data,
                "fred_data": fred_data,
                "spy_ohlcv": spy_ohlcv  # Add OHLCV data for Sperandeo analysis
            }
            self.data_cache[date_str] = result
            return result

        except Exception as e:
            logging.error(f"Error fetching data for {date_str}: {e}")
            return None

class LLMAgent:
    """A reusable class to interact with the OpenRouter API."""
    def __init__(self, model: str, system_prompt: str):
        if not OPENROUTER_API_KEY:
            raise ValueError("OPENROUTER_API_KEY environment variable not set.")
        self.model = model
        self.system_prompt = system_prompt

    def generate_response(self, user_prompt: str, is_json: bool = False, retries: int = 3) -> any:
        logging.info(f"Invoking LLM agent with model {self.model}...")
        for attempt in range(retries):
            try:
                response = requests.post(
                    url="https://openrouter.ai/api/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
                        "Content-Type": "application/json"
                    },
                    data=json.dumps({
                        "model": self.model,
                        "messages": [
                            {"role": "system", "content": self.system_prompt},
                            {"role": "user", "content": user_prompt},
                        ],
                        "temperature": 0.7,
                        "max_tokens": 4000,
                    }),
                    timeout=60
                )
                response.raise_for_status()
                result = response.json()
                content = result["choices"][0]["message"]["content"]

                if is_json:
                    return json.loads(content)
                else:
                    return content
            except json.JSONDecodeError as e:
                logging.error(f"LLM did not return valid JSON on attempt {attempt+1}. Content: {content}")
                time.sleep(2 ** attempt)
            except Exception as e:
                logging.error(f"Error calling OpenRouter API on attempt {attempt+1}: {e}")
                time.sleep(2 ** attempt)

        logging.critical(f"LLM Agent failed after {retries} attempts.")
        return None


class SperandeoTechnicalAnalyst:
    """
    Enhanced Technical Analyst using Victor Sperandeo's methodology.
    Replaces the basic technical_analyst with sophisticated reversal analysis.
    """
    def __init__(self, model: str):
        if not OPENROUTER_API_KEY:
            raise ValueError("OPENROUTER_API_KEY environment variable not set.")
        self.model = model
        # Simple system prompt since we construct detailed prompts dynamically
        self.system_prompt = "You are Victor Sperandeo, providing technical analysis based on your proven methodology."

    def analyze(self, raw_data: dict) -> str:
        """
        Perform Sperandeo technical analysis on the provided market data.
        """
        try:
            # Check if we have OHLCV data for SPY
            spy_ohlcv = raw_data.get('spy_ohlcv')
            if spy_ohlcv is None or spy_ohlcv.empty:
                logging.warning("No SPY OHLCV data available. Falling back to basic analysis.")
                return self._fallback_analysis(raw_data)

            # Generate Sperandeo features
            logging.info("Generating Sperandeo technical features...")
            sperandeo_features = generate_sperandeo_features(spy_ohlcv)

            # Construct detailed prompt
            prompt = construct_sperandeo_prompt(
                spy_ohlcv,
                sperandeo_features,
                raw_data['market_data'],
                raw_data['fred_data']
            )

            # Get LLM analysis
            logging.info("Requesting Sperandeo technical analysis from LLM...")
            for attempt in range(3):
                try:
                    response = requests.post(
                        url="https://openrouter.ai/api/v1/chat/completions",
                        headers={
                            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
                            "Content-Type": "application/json"
                        },
                        data=json.dumps({
                            "model": self.model,
                            "messages": [
                                {"role": "system", "content": self.system_prompt},
                                {"role": "user", "content": prompt},
                            ],
                            "temperature": 0.3,
                            "max_tokens": 4000,
                        }),
                        timeout=60
                    )
                    response.raise_for_status()
                    result = response.json()
                    analysis = result["choices"][0]["message"]["content"]
                    logging.info("Sperandeo technical analysis completed successfully.")
                    return analysis

                except Exception as e:
                    logging.error(f"Error in Sperandeo analysis attempt {attempt+1}: {e}")
                    if attempt < 2:
                        time.sleep(2 ** attempt)
                    else:
                        logging.error("All Sperandeo analysis attempts failed. Using fallback.")
                        return self._fallback_analysis(raw_data)

        except Exception as e:
            logging.error(f"Error in Sperandeo technical analysis: {e}")
            return self._fallback_analysis(raw_data)

    def _fallback_analysis(self, raw_data: dict) -> str:
        """
        Fallback to basic technical analysis if Sperandeo analysis fails.
        """
        market_data = raw_data.get('market_data', pd.DataFrame())
        if market_data.empty:
            return "Technical analysis unavailable due to data issues."

        try:
            spy_close = market_data.get('SPY', pd.Series())
            if spy_close.empty:
                return "SPY data unavailable for technical analysis."

            current_price = spy_close.iloc[-1]
            recent_high = spy_close.tail(20).max()
            recent_low = spy_close.tail(20).min()

            # Simple trend analysis
            sma_20 = spy_close.tail(20).mean()
            trend = "Uptrend" if current_price > sma_20 else "Downtrend"

            return f"""**TECHNICAL ANALYSIS (Fallback Mode)**

**Primary Trend:** {trend} - Current price ${current_price:.2f} vs 20-day average ${sma_20:.2f}

**Key Levels:**
- Recent High: ${recent_high:.2f}
- Recent Low: ${recent_low:.2f}
- Support/Resistance: Price is {'above' if current_price > sma_20 else 'below'} key moving average

**Recent Momentum:** {'Bullish' if current_price > sma_20 else 'Bearish'} based on price relative to recent average

Note: This is a simplified analysis due to technical issues with advanced Sperandeo methodology."""

        except Exception as e:
            logging.error(f"Error in fallback analysis: {e}")
            return "Technical analysis temporarily unavailable due to system issues."

class BacktestEngine:
    """Orchestrates the entire multi-agent backtesting simulation."""
    def __init__(self, start_date: str, end_date: str):
        self.start_date = datetime.strptime(start_date, '%Y-%m-%d')
        self.end_date = datetime.strptime(end_date, '%Y-%m-%d')
        self.data_fetcher = DataFetcher()
        self.results = []
        self.portfolio = {'position': 'FLAT', 'description': 'No active positions.'}

        # Initialize agents - use SperandeoTechnicalAnalyst for technical analysis
        self.agents = {}
        for name, prompt in AGENT_PROMPTS.items():
            if name == 'technical_analyst':
                # Use the enhanced Sperandeo technical analyst
                self.agents[name] = SperandeoTechnicalAnalyst(model=LLM_MODEL)
            else:
                # Use standard LLM agents for other roles
                self.agents[name] = LLMAgent(model=LLM_MODEL, system_prompt=prompt)

        logging.info("BacktestEngine initialized with all agents, including Sperandeo Technical Analyst.")

    def _format_data_for_prompt(self, data: dict) -> str:
        if not data:
            return "Data could not be fetched."

        market_str = data['market_data'].to_string()
        fred_df = data['fred_data'].rename(columns=FRED_SERIES)
        fred_str = fred_df.to_string()

        return f"""
<Market_Data_Last_{ANALYSIS_WINDOW_DAYS}_Days>
{market_str}
</Market_Data_Last_{ANALYSIS_WINDOW_DAYS}_Days>

<Macroeconomic_Data_Last_{ANALYSIS_WINDOW_DAYS}_Days>
{fred_str}
</Macroeconomic_Data_Last_{ANALYSIS_WINDOW_DAYS}_Days>
"""

    def run_simulation_step(self, current_date: datetime):
        logging.info(f"--- Running simulation for {current_date.strftime('%Y-%m-%d')} ---")
        
        raw_data = self.data_fetcher.get_market_data(current_date)
        if not raw_data:
            logging.error("Skipping day due to data fetching failure.")
            return
        
        data_prompt = self._format_data_for_prompt(raw_data)

        # 1. Analysis Phase
        # Use the enhanced Sperandeo technical analyst
        tech_analysis = self.agents['technical_analyst'].analyze(raw_data)
        econ_sentiment_analysis = self.agents['economist_sentiment_analyst'].generate_response(data_prompt)
        option_pricing_analysis = self.agents['option_pricing_analyst'].generate_response(data_prompt, is_json=True)
        
        if not all([tech_analysis, econ_sentiment_analysis, option_pricing_analysis]):
            logging.error("Skipping day due to analysis agent failure.")
            return
            
        logging.info(f"Option Pricing Assessment: {option_pricing_analysis.get('vol_assessment')}")

        # 2. Debate Phase
        debate_prompt = f"""
<Technical_Analysis_Report>
{tech_analysis}
</Technical_Analysis_Report>

<Economist_And_Sentiment_Report>
{econ_sentiment_analysis}
</Economist_And_Sentiment_Report>

<Option_Pricing_Analysis>
{json.dumps(option_pricing_analysis, indent=2)}
</Option_Pricing_Analysis>

Based on all three reports, construct your argument.
"""
        bull_argument = self.agents['bullish_strategist'].generate_response(debate_prompt)
        bear_argument = self.agents['bearish_strategist'].generate_response(debate_prompt)

        if not bull_argument or not bear_argument:
            logging.error("Skipping day due to strategist agent failure.")
            return

        # 3. Decision Phase
        trader_prompt = f"""
<Current_Portfolio_Position>
{json.dumps(self.portfolio, indent=2)}
</Current_Portfolio_Position>

<Technical_Analysis_Report>
{tech_analysis}
</Technical_Analysis_Report>

<Economist_And_Sentiment_Report>
{econ_sentiment_analysis}
</Economist_And_Sentiment_Report>

<Option_Pricing_Analysis>
{json.dumps(option_pricing_analysis, indent=2)}
</Option_Pricing_Analysis>

<Bullish_Case>
{bull_argument}
</Bullish_Case>

<Bearish_Case>
{bear_argument}
</Bearish_Case>

Synthesize ALL available information and provide your final trading decision as a JSON object.
"""
        final_decision = self.agents['chief_trader'].generate_response(trader_prompt, is_json=True)

        if not final_decision:
            logging.error("Skipping day due to chief trader agent failure.")
            return

        logging.info(f"Final Decision: {final_decision.get('decision')} | Strategy: {final_decision.get('strategy')}")

        # 4. Logging and State Update (Simplified)
        self.results.append({
            "date": current_date.strftime('%Y-%m-%d'),
            "current_position": self.portfolio['position'],
            "technical_analysis": tech_analysis,
            "econ_sentiment_analysis": econ_sentiment_analysis,
            "option_pricing_analysis_json": option_pricing_analysis,
            "bull_argument": bull_argument,
            "bear_argument": bear_argument,
            "final_decision_json": final_decision
        })
        
        # Simple state update: if a trade is made, portfolio is no longer FLAT.
        # A real implementation would parse the decision and update holdings.
        if final_decision.get('strategy') != 'No Trade':
            self.portfolio['position'] = final_decision.get('decision', 'UNKNOWN')
            self.portfolio['description'] = f"Holding a {self.portfolio['position']} position based on the strategy: {final_decision.get('strategy')}"
        else:
            # If "No Trade", we could choose to flatten or hold. For simplicity, we'll assume we flatten.
            self.portfolio = {'position': 'FLAT', 'description': 'No active positions.'}


    def run_backtest(self):
        all_dates = pd.to_datetime(pd.bdate_range(self.start_date, self.end_date))
        for current_date in all_dates:
            self.run_simulation_step(current_date)
            time.sleep(1)

        logging.info("--- Backtest complete ---")
        self.save_results()

    def save_results(self):
        if not self.results:
            logging.warning("No results to save.")
            return

        df = pd.json_normalize(self.results, sep='_')
        filename = f"trading_agents_v2_results_{self.start_date.strftime('%Y%m%d')}_{self.end_date.strftime('%Y%m%d')}.csv"
        df.to_csv(filename, index=False)
        logging.info(f"Results saved to {filename}")


# --- MAIN EXECUTION BLOCK ---

if __name__ == "__main__":
    if not OPENROUTER_API_KEY:
        print("FATAL: OPENROUTER_API_KEY environment variable is not set.")
        print("Please set it before running the script (e.g., 'export OPENROUTER_API_KEY=your_key').")
    else:
        # Define the backtest period here
        BACKTEST_START_DATE = "2023-11-01"
        BACKTEST_END_DATE = "2023-11-10"
        
        logging.info(f"Starting Multi-Agent Trading Simulation v2 from {BACKTEST_START_DATE} to {BACKTEST_END_DATE}")
        
        engine = BacktestEngine(start_date=BACKTEST_START_DATE, end_date=BACKTEST_END_DATE)
        engine.run_backtest()