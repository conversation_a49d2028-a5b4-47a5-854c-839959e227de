# TradingAgents: Multi-Agent LLM Options Trading Framework
#
# Version: 2.0
# Description: This script implements an enhanced multi-agent LLM system for simulating SPY ETF options trading decisions.
# It features specialized analyst roles, including a quantitative-style option pricing analyst, and incorporates
# portfolio state awareness into the final trading decision.
#
# Author: AI Systems Architect & Quantitative Developer
# Date: 2024-05-17
#

import os
import json
import logging
import time
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import yfinance as yf
import pandas_datareader.data as web
from openrouter import Client

# --- CONFIGURATION ---

# 1. API Keys 
OPENROUTER_API_KEY = "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"
FRED_API_KEY = "dac90176cd11cbf8281068404bc30b7a"

# 2. LLM Configuration
LLM_MODEL = "x-ai/grok-4-fast:free"

# 3. Simulation Parameters
ANALYSIS_WINDOW_DAYS = 126 # Approx. 6 months of trading days
HV_WINDOW = 21 # Historical Volatility window (approx. 1 month)
DTE_CHOICES = [5, 8, 15, 22, 29, 36, 43, 57, 64, 85, 97, 113, 127] # Days to Expiration

# 4. Data Tickers and Series
MARKET_TICKERS = ["SPY", "^VIX", "^IRX"] # SPY ETF, VIX Index, 13-week T-bill (^IRX as risk-free rate)
FRED_SERIES = {
    "UNRATE": "Civilian Unemployment Rate",
    "CPIAUCSL": "Consumer Price Index for All Urban Consumers",
    "T10Y2Y": "10-Year Minus 2-Year Treasury Yield Spread"
}

# 5. Logging Setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# --- AGENT SYSTEM PROMPTS (Version 2.0) ---

AGENT_PROMPTS = {
    "technical_analyst": """
You are a Senior Technical Analyst. Your sole responsibility is to provide a clear, data-driven analysis of the SPY ETF's price action. Do not offer opinions or trading advice.

Your analysis must cover:
1.  **Primary Trend:** Identify the dominant trend (Uptrend, Downtrend, Range-Bound) over the provided data window.
2.  **Key Levels:** Identify the most significant support and resistance levels based on recent price history.
3.  **Recent Momentum:** Describe the price action over the last 5-10 trading days. Is momentum accelerating or decelerating?

Provide your output as a concise, structured report.
""",
    "economist_sentiment_analyst": """
You are a Macroeconomist and Sentiment Specialist. Your task is to analyze the provided economic (FRED) and volatility (VIX) data to paint a picture of the overall market mood and economic health.

Your analysis must cover:
1.  **Economic Outlook:** Based on Unemployment, CPI, and the Yield Curve, what is the current state of the economy? Are conditions inflationary, deflationary, expanding, or contracting?
2.  **Market Sentiment:** Interpret the VIX level and its recent trend. Is the market fearful or complacent? Is the 'fear gauge' rising or falling?
3.  **Synthesis:** Combine the economic and sentiment data. Is the market sentiment justified by the economic reality?

Provide your output as a single, coherent report.
""",
    "option_pricing_analyst": f"""
You are a quantitative analyst specializing in options volatility. Your task is to assess whether buying long SPY options is favorable based on the relationship between implied volatility (VIX) and historical volatility (HV). You must adhere to the fund's research findings.

**Core Logic:**
1.  **Compare VIX to HV:** The primary indicator is the spread between VIX and the {HV_WINDOW}-day HV.
2.  **Assess Favorability:**
    - If VIX is more than 10% above HV (e.g., VIX=20, HV=18), options are considered 'overpriced'. The premium drag makes buying them **unfavorable**.
    - If VIX is below HV, options are 'underpriced' and buying is **favorable**.
    - The default bias is **unfavorable** due to the consistent underperformance of long options found in academic research. You need a compelling reason (like VIX < HV) to call it favorable.
3.  **Directional Consideration:** A very high VIX might be acceptable for a bearish long put strategy if a crash is anticipated, but it is highly unfavorable for a long call strategy. Mention this nuance.
4.  **Carry Cost:** Note the impact of the risk-free rate (^IRX). A higher rate increases the carry cost for long option positions, making it an additional headwind for buyers.
5.  **Expiry Suggestion:**
    - If VIX is high or has spiked recently, suggest **'near-term'** expiries (<= 43 DTE) to capture the immediate move.
    - If VIX is low or compressed, suggest **'long-term'** expiries (> 43 DTE) for a better value proposition.

Your output MUST be a single JSON object with the following structure:
{{
    "vol_assessment": "'favorable' or 'unfavorable'",
    "calibrated_confidence": "An integer from 0 (no confidence in assessment) to 100 (absolute confidence)",
    "reasoning": "A detailed explanation covering your VIX vs. HV comparison, the impact of the risk-free rate, and the default unfavorable bias.",
    "suggested_expiry_type": "'near-term' or 'long-term'"
}}
""",
    "bullish_strategist": """
You are an aggressive and optimistic Bullish Strategist. Using the provided reports from the Technical, Economic, and Options analysts, build the strongest possible case for a positive (upward) move in the market.

-   Focus on any data point that supports a bullish outlook (e.g., price at support, positive economic data, favorable volatility).
-   Downplay or re-interpret any negative data.
-   Conclude with a clear "Bullish Case" summary and a confidence score for your argument.

Your output must be a text report ending with the line:
CONFIDENCE: [a number from 1 to 10]
""",
    "bearish_strategist": """
You are a cautious and pessimistic Bearish Strategist. Using the provided reports from the Technical, Economic, and Options analysts, build the strongest possible case for a negative (downward) move in the market.

-   Focus on any data point that supports a bearish outlook (e.g., price at resistance, negative economic data, unfavorable volatility).
-   Downplay or re-interpret any positive data.
-   Conclude with a clear "Bearish Case" summary and a confidence score for your argument.

Your output must be a text report ending with the line:
CONFIDENCE: [a number from 1 to 10]
""",
    "chief_trader": f"""
You are the Chief Trader. You have received reports from your Technical, Economic, and Option Pricing analysts, the conflicting arguments from your strategists, and information on your current portfolio.

Your task is to synthesize all this information and make a single, decisive, and actionable trading decision for SPY options for the next trading day. You must weigh all evidence, including the quantitative assessment of option pricing.

Your final output MUST be a single JSON object with the following structure:
{{
    "decision": "BULLISH", "BEARISH", or "NEUTRAL",
    "strategy": "A specific options strategy (e.g., 'Buy Call', 'Buy Put', 'Bull Call Spread', 'Bear Put Spread', 'Iron Condor', 'No Trade')",
    "dte": An integer representing the chosen Days to Expiration from this list: {DTE_CHOICES},
    "confidence": An integer from 1 (very low) to 10 (very high) in your overall decision,
    "reasoning": "A detailed paragraph explaining how you synthesized ALL inputs (technical, economic, volatility, strategist debate, and current position) to arrive at your final decision."
}}
"""
}


# --- CORE FRAMEWORK CLASSES ---

class DataFetcher:
    """Handles all external data fetching and pre-processing, including HV calculation."""
    def __init__(self):
        self.data_cache = {}

    def _calculate_hv(self, series: pd.Series) -> pd.Series:
        """Calculates annualized historical volatility."""
        log_returns = np.log(series / series.shift(1))
        hv = log_returns.rolling(window=HV_WINDOW).std() * np.sqrt(252)
        return hv * 100 # Return as percentage

    def get_market_data(self, current_date: datetime) -> dict:
        date_str = current_date.strftime('%Y-%m-%d')
        if date_str in self.data_cache:
            return self.data_cache[date_str]

        logging.info(f"Fetching data for window ending on {date_str}...")
        end_date = current_date
        start_date = end_date - timedelta(days=ANALYSIS_WINDOW_DAYS * 1.7)

        try:
            market_data_raw = yf.download(MARKET_TICKERS, start=start_date, end=end_date, progress=False)
            if market_data_raw.empty:
                logging.error(f"No market data found for the period ending {date_str}.")
                return None
            
            market_data = market_data_raw['Close'].tail(ANALYSIS_WINDOW_DAYS).copy()
            market_data['HV'] = self._calculate_hv(market_data['SPY'])

            fred_data = web.DataReader(list(FRED_SERIES.keys()), 'fred', start_date, end_date)
            fred_data = fred_data.tail(ANALYSIS_WINDOW_DAYS)
            
            # Fill NaNs robustly
            for df in [market_data, fred_data]:
                df.fillna(method='ffill', inplace=True)
                df.fillna(method='bfill', inplace=True)

            if market_data.isnull().values.any() or fred_data.isnull().values.any():
                 logging.critical(f"Unrecoverable NaN values in data for {date_str}. Skipping.")
                 return None

            result = {
                "market_data": market_data,
                "fred_data": fred_data
            }
            self.data_cache[date_str] = result
            return result

        except Exception as e:
            logging.error(f"Error fetching data for {date_str}: {e}")
            return None

class LLMAgent:
    """A reusable class to interact with the OpenRouter API."""
    def __init__(self, model: str, system_prompt: str):
        if not OPENROUTER_API_KEY:
            raise ValueError("OPENROUTER_API_KEY environment variable not set.")
        self.client = Client(api_key=OPENROUTER_API_KEY)
        self.model = model
        self.system_prompt = system_prompt

    def generate_response(self, user_prompt: str, is_json: bool = False, retries: int = 3) -> any:
        logging.info(f"Invoking LLM agent with model {self.model}...")
        for attempt in range(retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": user_prompt},
                    ],
                    response_format={"type": "json_object"} if is_json else None
                )
                content = response.choices[0].message.content
                
                if is_json:
                    return json.loads(content)
                else:
                    return content
            except json.JSONDecodeError as e:
                logging.error(f"LLM did not return valid JSON on attempt {attempt+1}. Content: {content}")
                time.sleep(2 ** attempt)
            except Exception as e:
                logging.error(f"Error calling OpenRouter API on attempt {attempt+1}: {e}")
                time.sleep(2 ** attempt)
        
        logging.critical(f"LLM Agent failed after {retries} attempts.")
        return None

class BacktestEngine:
    """Orchestrates the entire multi-agent backtesting simulation."""
    def __init__(self, start_date: str, end_date: str):
        self.start_date = datetime.strptime(start_date, '%Y-%m-%d')
        self.end_date = datetime.strptime(end_date, '%Y-%m-%d')
        self.data_fetcher = DataFetcher()
        self.results = []
        self.portfolio = {'position': 'FLAT', 'description': 'No active positions.'}

        self.agents = {
            name: LLMAgent(model=LLM_MODEL, system_prompt=prompt)
            for name, prompt in AGENT_PROMPTS.items()
        }
        logging.info("BacktestEngine initialized with all agents.")

    def _format_data_for_prompt(self, data: dict) -> str:
        if not data:
            return "Data could not be fetched."

        market_str = data['market_data'].to_string()
        fred_df = data['fred_data'].rename(columns=FRED_SERIES)
        fred_str = fred_df.to_string()

        return f"""
<Market_Data_Last_{ANALYSIS_WINDOW_DAYS}_Days>
{market_str}
</Market_Data_Last_{ANALYSIS_WINDOW_DAYS}_Days>

<Macroeconomic_Data_Last_{ANALYSIS_WINDOW_DAYS}_Days>
{fred_str}
</Macroeconomic_Data_Last_{ANALYSIS_WINDOW_DAYS}_Days>
"""

    def run_simulation_step(self, current_date: datetime):
        logging.info(f"--- Running simulation for {current_date.strftime('%Y-%m-%d')} ---")
        
        raw_data = self.data_fetcher.get_market_data(current_date)
        if not raw_data:
            logging.error("Skipping day due to data fetching failure.")
            return
        
        data_prompt = self._format_data_for_prompt(raw_data)

        # 1. Analysis Phase
        tech_analysis = self.agents['technical_analyst'].generate_response(data_prompt)
        econ_sentiment_analysis = self.agents['economist_sentiment_analyst'].generate_response(data_prompt)
        option_pricing_analysis = self.agents['option_pricing_analyst'].generate_response(data_prompt, is_json=True)
        
        if not all([tech_analysis, econ_sentiment_analysis, option_pricing_analysis]):
            logging.error("Skipping day due to analysis agent failure.")
            return
            
        logging.info(f"Option Pricing Assessment: {option_pricing_analysis.get('vol_assessment')}")

        # 2. Debate Phase
        debate_prompt = f"""
<Technical_Analysis_Report>
{tech_analysis}
</Technical_Analysis_Report>

<Economist_And_Sentiment_Report>
{econ_sentiment_analysis}
</Economist_And_Sentiment_Report>

<Option_Pricing_Analysis>
{json.dumps(option_pricing_analysis, indent=2)}
</Option_Pricing_Analysis>

Based on all three reports, construct your argument.
"""
        bull_argument = self.agents['bullish_strategist'].generate_response(debate_prompt)
        bear_argument = self.agents['bearish_strategist'].generate_response(debate_prompt)

        if not bull_argument or not bear_argument:
            logging.error("Skipping day due to strategist agent failure.")
            return

        # 3. Decision Phase
        trader_prompt = f"""
<Current_Portfolio_Position>
{json.dumps(self.portfolio, indent=2)}
</Current_Portfolio_Position>

<Technical_Analysis_Report>
{tech_analysis}
</Technical_Analysis_Report>

<Economist_And_Sentiment_Report>
{econ_sentiment_analysis}
</Economist_And_Sentiment_Report>

<Option_Pricing_Analysis>
{json.dumps(option_pricing_analysis, indent=2)}
</Option_Pricing_Analysis>

<Bullish_Case>
{bull_argument}
</Bullish_Case>

<Bearish_Case>
{bear_argument}
</Bearish_Case>

Synthesize ALL available information and provide your final trading decision as a JSON object.
"""
        final_decision = self.agents['chief_trader'].generate_response(trader_prompt, is_json=True)

        if not final_decision:
            logging.error("Skipping day due to chief trader agent failure.")
            return

        logging.info(f"Final Decision: {final_decision.get('decision')} | Strategy: {final_decision.get('strategy')}")

        # 4. Logging and State Update (Simplified)
        self.results.append({
            "date": current_date.strftime('%Y-%m-%d'),
            "current_position": self.portfolio['position'],
            "technical_analysis": tech_analysis,
            "econ_sentiment_analysis": econ_sentiment_analysis,
            "option_pricing_analysis_json": option_pricing_analysis,
            "bull_argument": bull_argument,
            "bear_argument": bear_argument,
            "final_decision_json": final_decision
        })
        
        # Simple state update: if a trade is made, portfolio is no longer FLAT.
        # A real implementation would parse the decision and update holdings.
        if final_decision.get('strategy') != 'No Trade':
            self.portfolio['position'] = final_decision.get('decision', 'UNKNOWN')
            self.portfolio['description'] = f"Holding a {self.portfolio['position']} position based on the strategy: {final_decision.get('strategy')}"
        else:
            # If "No Trade", we could choose to flatten or hold. For simplicity, we'll assume we flatten.
            self.portfolio = {'position': 'FLAT', 'description': 'No active positions.'}


    def run_backtest(self):
        all_dates = pd.to_datetime(pd.bdate_range(self.start_date, self.end_date))
        for current_date in all_dates:
            self.run_simulation_step(current_date)
            time.sleep(1)

        logging.info("--- Backtest complete ---")
        self.save_results()

    def save_results(self):
        if not self.results:
            logging.warning("No results to save.")
            return

        df = pd.json_normalize(self.results, sep='_')
        filename = f"trading_agents_v2_results_{self.start_date.strftime('%Y%m%d')}_{self.end_date.strftime('%Y%m%d')}.csv"
        df.to_csv(filename, index=False)
        logging.info(f"Results saved to {filename}")


# --- MAIN EXECUTION BLOCK ---

if __name__ == "__main__":
    if not OPENROUTER_API_KEY:
        print("FATAL: OPENROUTER_API_KEY environment variable is not set.")
        print("Please set it before running the script (e.g., 'export OPENROUTER_API_KEY=your_key').")
    else:
        # Define the backtest period here
        BACKTEST_START_DATE = "2023-11-01"
        BACKTEST_END_DATE = "2023-11-10"
        
        logging.info(f"Starting Multi-Agent Trading Simulation v2 from {BACKTEST_START_DATE} to {BACKTEST_END_DATE}")
        
        engine = BacktestEngine(start_date=BACKTEST_START_DATE, end_date=BACKTEST_END_DATE)
        engine.run_backtest()